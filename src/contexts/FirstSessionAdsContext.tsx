import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import appStateService from '../services/appStateService';

interface FirstSessionAdsContextType {
  shouldShowAds: boolean;
  isLoading: boolean;
  error: string | null;
}

const FirstSessionAdsContext = createContext<FirstSessionAdsContextType | undefined>(undefined);

interface FirstSessionAdsProviderProps {
  children: ReactNode;
}

export const FirstSessionAdsProvider: React.FC<FirstSessionAdsProviderProps> = ({ children }) => {
  const [shouldShowAds, setShouldShowAds] = useState<boolean>(false); // Default to false (hide ads)
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeAdsState();
  }, []);

  const initializeAdsState = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('[FirstSessionAdsProvider] Initializing ads state...');
      
      // Check if ads should be shown in this session
      const shouldShow = await appStateService.shouldShowAdsInFirstSession();
      
      setShouldShowAds(shouldShow);
      
      console.log(`[FirstSessionAdsProvider] Global ads state initialized: shouldShowAds=${shouldShow}`);
      
      if (shouldShow) {
        console.log('[FirstSessionAdsProvider] ✅ Ads will be shown globally');
      } else {
        console.log('[FirstSessionAdsProvider] ❌ Ads will be hidden globally (first session)');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('[FirstSessionAdsProvider] Error initializing ads state:', err);
      
      // Default to not showing ads in case of error
      setShouldShowAds(false);
      console.log('[FirstSessionAdsProvider] ❌ Error occurred - ads will be hidden globally');
    } finally {
      setIsLoading(false);
    }
  };

  const value: FirstSessionAdsContextType = {
    shouldShowAds,
    isLoading,
    error,
  };

  return (
    <FirstSessionAdsContext.Provider value={value}>
      {children}
    </FirstSessionAdsContext.Provider>
  );
};

export const useFirstSessionAdsContext = (): FirstSessionAdsContextType => {
  const context = useContext(FirstSessionAdsContext);
  if (context === undefined) {
    throw new Error('useFirstSessionAdsContext must be used within a FirstSessionAdsProvider');
  }
  return context;
};
