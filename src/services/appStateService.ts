import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  FIRST_LAUNCH_COMPLETED: 'first_launch_completed',
  APP_VERSION: 'app_version',
  FIRST_SESSION_ADS_SHOWN: 'first_session_ads_shown',
  APP_INSTALL_TIMESTAMP: 'app_install_timestamp',
} as const;

class AppStateService {
  /**
   * Check if this is the first launch after app installation
   * Returns true if this is the first launch, false otherwise
   */
  async isFirstLaunch(): Promise<boolean> {
    try {
      const firstLaunchCompleted = await AsyncStorage.getItem(
        STORAGE_KEYS.FIRST_LAUNCH_COMPLETED,
      );

      // If the key doesn't exist, this is the first launch
      return firstLaunchCompleted === null;
    } catch (error) {
      console.error('[AppStateService] Error checking first launch:', error);
      // In case of error, assume it's first launch to be safe
      return true;
    }
  }

  /**
   * Mark the first launch as completed
   * This should be called when user completes the onboarding (InfoScreen)
   */
  async markFirstLaunchCompleted(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.FIRST_LAUNCH_COMPLETED, 'true');
      console.log('[AppStateService] First launch marked as completed');
    } catch (error) {
      console.error(
        '[AppStateService] Error marking first launch completed:',
        error,
      );
      throw error;
    }
  }

  /**
   * Reset first launch flag (useful for testing)
   * This will make the app show InfoScreen on next launch
   */
  async resetFirstLaunchFlag(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.FIRST_LAUNCH_COMPLETED);
      console.log('[AppStateService] First launch flag reset');
    } catch (error) {
      console.error(
        '[AppStateService] Error resetting first launch flag:',
        error,
      );
      throw error;
    }
  }

  /**
   * Get the stored app version
   */
  async getStoredAppVersion(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.APP_VERSION);
    } catch (error) {
      console.error(
        '[AppStateService] Error getting stored app version:',
        error,
      );
      return null;
    }
  }

  /**
   * Store the current app version
   */
  async setAppVersion(version: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.APP_VERSION, version);
    } catch (error) {
      console.error('[AppStateService] Error storing app version:', error);
      throw error;
    }
  }

  /**
   * Check if ads should be shown in the current session
   * Returns false if this is the first session ever (ads should be hidden)
   * Returns true if this is a subsequent session (ads should be displayed)
   */
  async shouldShowAdsInFirstSession(): Promise<boolean> {
    try {
      console.log('[AppStateService] Checking install timestamp...');

      // Check if we have an install timestamp
      const installTimestamp = await AsyncStorage.getItem(
        STORAGE_KEYS.APP_INSTALL_TIMESTAMP,
      );

      console.log(`[AppStateService] Install timestamp: ${installTimestamp}`);

      if (!installTimestamp) {
        // No install timestamp means this is the very first app launch
        // Set the install timestamp for future reference
        const currentTime = Date.now().toString();
        await AsyncStorage.setItem(
          STORAGE_KEYS.APP_INSTALL_TIMESTAMP,
          currentTime,
        );
        console.log(
          `[AppStateService] ❌ First app install detected - setting timestamp: ${currentTime} - ads will be hidden`,
        );
        return false;
      } else {
        // Install timestamp exists, this is not the first session
        console.log(
          `[AppStateService] ✅ Subsequent app session (timestamp: ${installTimestamp}) - ads will be shown`,
        );
        return true;
      }
    } catch (error) {
      console.error(
        '[AppStateService] Error checking first session ads:',
        error,
      );
      // In case of error, don't show ads to be safe
      console.log('[AppStateService] ❌ Error occurred - ads will be hidden');
      return false;
    }
  }

  /**
   * Mark that ads have been shown (should be called on second app launch)
   * This should be called when the app starts and it's not the first launch
   */
  async markAdsShown(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.FIRST_SESSION_ADS_SHOWN, 'true');
      console.log('[AppStateService] First session ads marked as shown');
    } catch (error) {
      console.error('[AppStateService] Error marking ads shown:', error);
      throw error;
    }
  }

  /**
   * Clear all app state data (useful for testing or app reset)
   */
  async clearAllAppState(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.FIRST_LAUNCH_COMPLETED,
        STORAGE_KEYS.APP_VERSION,
        STORAGE_KEYS.FIRST_SESSION_ADS_SHOWN,
        STORAGE_KEYS.APP_INSTALL_TIMESTAMP,
      ]);
      console.log('[AppStateService] All app state cleared');
    } catch (error) {
      console.error('[AppStateService] Error clearing app state:', error);
      throw error;
    }
  }

  /**
   * Debug method to log current app state
   */
  async debugAppState(): Promise<void> {
    try {
      const isFirst = await this.isFirstLaunch();
      const version = await this.getStoredAppVersion();
      const shouldShowAds = await this.shouldShowAdsInFirstSession();

      console.log('\n🔍 === APP STATE DEBUG ===');
      console.log(`Is First Launch: ${isFirst}`);
      console.log(`Should Show Ads: ${shouldShowAds}`);
      console.log(`Stored Version: ${version}`);
      console.log('========================\n');
    } catch (error) {
      console.error('[AppStateService] Error debugging app state:', error);
    }
  }
}

export default new AppStateService();
