// React Native WebSocket Service for Phone Ratings
class PhoneRatingsService {
  constructor() {
    this.LICENSE = 'p4n0jt8njtg3';
    this.websocket = null;
    this.isConnecting = false;
    this.messageQueue = [];
    this.pendingVotes = new Map(); // Track pending votes by phone number
    this.phoneData = {}; // Store phone rating data
  }

  // Initialize WebSocket connection
  connect() {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      console.log('[PhoneRatings] WebSocket already connected');
      return Promise.resolve();
    }

    if (this.isConnecting) {
      console.log(
        '[PhoneRatings] WebSocket connection already in progress, waiting...',
      );
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('Connection attempt failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    this.isConnecting = true;
    console.log(
      '[PhoneRatings] Starting WebSocket connection to wss://phoneratings.tariffplansindia.com:8001',
    );

    return new Promise((resolve, reject) => {
      const connectionTimeout = setTimeout(() => {
        console.log('[PhoneRatings] WebSocket connection timeout');
        this.isConnecting = false;
        if (this.websocket) {
          this.websocket.close();
          this.websocket = null;
        }
        reject(new Error('WebSocket connection timeout'));
      }, 10000); // 10 second timeout

      try {
        this.websocket = new WebSocket(
          'wss://phoneratings.tariffplansindia.com:8001',
        );

        this.websocket.onopen = () => {
          console.log('[PhoneRatings] ✅ WebSocket connected successfully');
          clearTimeout(connectionTimeout);
          this.isConnecting = false;

          // Process any queued messages
          while (this.messageQueue.length > 0) {
            const queuedData = this.messageQueue.shift();
            this.sendRatingData(queuedData);
          }

          resolve();
        };

        this.websocket.onclose = event => {
          console.log(
            '[PhoneRatings] 🔌 WebSocket connection closed:',
            'Code:',
            event.code,
            'Reason:',
            event.reason,
            'WasClean:',
            event.wasClean,
          );
          clearTimeout(connectionTimeout);
          this.websocket = null;
          this.isConnecting = false;
        };

        this.websocket.onmessage = evt => {
          console.log('[PhoneRatings] 📨 Raw response from server:', evt.data);
          console.log('[PhoneRatings] 📨 Response type:', typeof evt.data);
          console.log(
            '[PhoneRatings] 📨 Response length:',
            evt.data ? evt.data.length : 'null/undefined',
          );

          try {
            // Handle empty or whitespace-only responses
            if (!evt.data || evt.data.trim() === '') {
              console.log(
                '[PhoneRatings] ⚠️ Received empty response from server',
              );
              this.handlePlainTextResponse('empty');
              return;
            }

            const response = JSON.parse(evt.data);
            console.log('[PhoneRatings] ✅ Parsed JSON response:', response);
            this.handleRatingResponse(response);
          } catch (ex) {
            console.log(
              '[PhoneRatings] ⚠️ Response is not JSON, treating as plain text:',
              evt.data,
            );
            console.log('[PhoneRatings] ⚠️ JSON parse error:', ex.message);
            this.handlePlainTextResponse(evt.data);
          }
        };

        this.websocket.onerror = evt => {
          console.log('[PhoneRatings] ❌ WebSocket error:', evt);
          clearTimeout(connectionTimeout);
          this.isConnecting = false;
          this.websocket = null;
          reject(new Error('WebSocket connection failed'));
        };
      } catch (error) {
        console.log('[PhoneRatings] ❌ Error creating WebSocket:', error);
        clearTimeout(connectionTimeout);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Send vote for a phone number
  async sendVote(phoneNumber, isUpvote) {
    console.log(
      `[PhoneRatings] 📤 Attempting to send ${
        isUpvote ? 'upvote' : 'downvote'
      } for phone: ${phoneNumber}`,
    );

    // Initialize phone data if not exists
    if (!this.phoneData[phoneNumber]) {
      this.phoneData[phoneNumber] = new PhoneRatings(0, 0);
    }

    const phoneRating = this.phoneData[phoneNumber];

    // Update local rating
    if (isUpvote) {
      phoneRating.doUp();
    } else {
      phoneRating.doDown();
    }

    return new Promise(async (resolve, reject) => {
      try {
        // Check if already connected, if not try to connect
        if (!this.isConnected()) {
          console.log(
            '[PhoneRatings] 🔌 WebSocket not connected, attempting to connect...',
          );
          const connected = await this.initializeConnection(0, 2); // Try up to 3 times

          if (!connected) {
            console.log(
              '[PhoneRatings] ❌ Failed to establish WebSocket connection for voting',
            );
            // Still allow local vote counting even if WebSocket fails
            console.log(
              '[PhoneRatings] 📋 Allowing local vote counting despite connection failure',
            );
            resolve({
              success: true,
              timeout: true,
              connectionFailed: true,
              phoneNumber,
              isUpvote,
            });
            return;
          }
        }

        console.log(
          '[PhoneRatings] ✅ WebSocket connected, sending vote data...',
        );

        // Store the promise resolvers for this vote
        this.pendingVotes.set(phoneNumber, {
          resolve,
          reject,
          phoneNumber,
          isUpvote,
        });

        // Set timeout for vote response
        setTimeout(() => {
          if (this.pendingVotes.has(phoneNumber)) {
            this.pendingVotes.delete(phoneNumber);
            console.log(
              '[PhoneRatings] ⏰ Vote response timeout for phone:',
              phoneNumber,
            );
            resolve({success: true, timeout: true, phoneNumber, isUpvote}); // Still consider it successful but with timeout
          }
        }, 10000); // 10 second timeout

        // Send the updated ratings for this specific phone number
        this.sendUpdatedRatingsForPhone(phoneNumber);
      } catch (error) {
        console.log(
          '[PhoneRatings] ❌ Failed to connect WebSocket:',
          error.message,
        );
        // Allow local vote counting even if WebSocket fails
        console.log(
          '[PhoneRatings] 📋 Allowing local vote counting despite WebSocket error',
        );
        resolve({
          success: true,
          timeout: true,
          error: error.message,
          phoneNumber,
          isUpvote,
        });
      }
    });
  }

  // Send updated ratings to server
  sendUpdatedRatings() {
    const toBeUpdated = {};
    let count = 0;

    for (const phone in this.phoneData) {
      if (this.phoneData.hasOwnProperty(phone)) {
        const phoneRating = this.phoneData[phone];

        if (phoneRating.shouldUpdateRemotely()) {
          toBeUpdated[phone] = {
            up:
              -1 * phoneRating.remoteUpVal +
              phoneRating.changedUp -
              phoneRating.originalUp,
            down:
              -1 * phoneRating.remoteDownVal +
              phoneRating.changedDown -
              phoneRating.originalDown,
          };
          count++;
        }
      }
    }

    console.log('[PhoneRatings] Total to be updated:', count);

    if (count === 0) {
      return;
    }

    const ratingData = {
      l: this.LICENSE,
      op: 'updateratings',
      numbers: toBeUpdated,
      url: 'app://indiacustomercare.com', // React Native app identifier
      finger: this.getFingerPrint(),
    };

    this.sendRatingData(ratingData);
  }

  // Send updated ratings for a specific phone number only
  sendUpdatedRatingsForPhone(phoneNumber) {
    console.log(
      '[PhoneRatings] Sending update for specific phone:',
      phoneNumber,
    );

    if (!this.phoneData[phoneNumber]) {
      console.log('[PhoneRatings] No data found for phone:', phoneNumber);
      return;
    }

    const phoneRating = this.phoneData[phoneNumber];

    // Check if this phone number has changes to send
    if (
      phoneRating.remoteDown === phoneRating.changedDown &&
      phoneRating.remoteUp === phoneRating.changedUp
    ) {
      console.log('[PhoneRatings] No changes to send for phone:', phoneNumber);
      return;
    }

    const toBeUpdated = {};
    toBeUpdated[phoneNumber] = {
      up:
        -1 * phoneRating.remoteUpVal +
        phoneRating.changedUp -
        phoneRating.originalUp,
      down:
        -1 * phoneRating.remoteDownVal +
        phoneRating.changedDown -
        phoneRating.originalDown,
    };

    console.log(
      '[PhoneRatings] Sending update for phone:',
      phoneNumber,
      toBeUpdated[phoneNumber],
    );

    const ratingData = {
      l: this.LICENSE,
      op: 'updateratings',
      numbers: toBeUpdated,
      url: 'app://indiacustomercare.com', // React Native app identifier
      finger: this.getFingerPrint(),
    };

    this.sendRatingData(ratingData);
  }

  // Internal method to send data through WebSocket
  sendRatingData(ratingData) {
    // Try the original approach: create a new WebSocket for each message
    // This matches the working icc_client.js pattern
    console.log(
      '[PhoneRatings] 📤 Creating new WebSocket for rating data:',
      ratingData,
    );

    const ws = new WebSocket('wss://phoneratings.tariffplansindia.com:8001');

    ws.onopen = () => {
      console.log('[PhoneRatings] � New WebSocket opened for rating data');
      const payload = JSON.stringify(ratingData);
      ws.send(payload);
      console.log(
        '[PhoneRatings] ✅ Rating message sent via new WebSocket:',
        payload,
      );

      // Update phone data after sending
      for (const phone in ratingData.numbers) {
        if (ratingData.numbers.hasOwnProperty(phone)) {
          const phoneRating = this.phoneData[phone];
          phoneRating.updatedRemotely = true;
          phoneRating.lastModifiedTime = null;
          phoneRating.remoteDown = phoneRating.changedDown;
          phoneRating.remoteUp = phoneRating.changedUp;
          phoneRating.remoteUpVal =
            phoneRating.changedUp - phoneRating.originalUp;
          phoneRating.remoteDownVal =
            phoneRating.changedDown - phoneRating.originalDown;
        }
      }
    };

    ws.onmessage = evt => {
      console.log('[PhoneRatings] 📨 Response from new WebSocket:', evt.data);

      try {
        if (!evt.data || evt.data.trim() === '') {
          console.log('[PhoneRatings] ⚠️ Empty response, treating as success');
          this.handleRatingResponse({insertsuccess: true});
          return;
        }

        const response = JSON.parse(evt.data);
        console.log(
          '[PhoneRatings] ✅ Parsed response from new WebSocket:',
          response,
        );
        this.handleRatingResponse(response);
      } catch (ex) {
        console.log(
          '[PhoneRatings] ⚠️ Non-JSON response, treating as text:',
          evt.data,
        );
        this.handlePlainTextResponse(evt.data);
      }

      // Close the WebSocket after receiving response
      ws.close();
    };

    ws.onerror = error => {
      console.log('[PhoneRatings] ❌ New WebSocket error:', error);
      // Fallback to queuing
      this.messageQueue.push(ratingData);
    };

    ws.onclose = event => {
      console.log(
        '[PhoneRatings] 🔌 New WebSocket closed:',
        event.code,
        event.reason,
      );
    };
  }

  // Handle rating response from server
  handleRatingResponse(response) {
    console.log('[PhoneRatings] 🔄 Processing rating response:', response);

    // Check for success in multiple possible response formats
    const isSuccess =
      response &&
      (response.insertsuccess === true ||
        response.insertsuccess === 'true' ||
        response.success === true ||
        response.status === 'success' ||
        (typeof response === 'object' && Object.keys(response).length > 0));

    if (isSuccess) {
      console.log(
        '[PhoneRatings] 🎉 Rating update confirmed successful by server',
      );

      // Resolve all pending votes
      for (const [phoneNumber, pendingVote] of this.pendingVotes.entries()) {
        this.pendingVotes.delete(phoneNumber);
        pendingVote.resolve({
          success: true,
          confirmed: true,
          response: response,
          phoneNumber: pendingVote.phoneNumber,
          isUpvote: pendingVote.isUpvote,
        });
      }

      // Mark all phone data as updated remotely
      for (const phone in this.phoneData) {
        if (this.phoneData.hasOwnProperty(phone)) {
          this.phoneData[phone].updatedRemotely = true;
        }
      }
    } else {
      console.log(
        '[PhoneRatings] ❌ Rating update failed according to server response',
      );

      // Still resolve as successful but mark as unconfirmed for timeout handling
      for (const [phoneNumber, pendingVote] of this.pendingVotes.entries()) {
        this.pendingVotes.delete(phoneNumber);
        pendingVote.resolve({
          success: true,
          confirmed: false,
          response: response,
          phoneNumber: pendingVote.phoneNumber,
          isUpvote: pendingVote.isUpvote,
          message: response?.message || 'Server response unclear',
        });
      }
    }
  }

  // Handle plain text responses from server
  handlePlainTextResponse(textResponse) {
    console.log(
      '[PhoneRatings] 🔄 Processing plain text response:',
      textResponse,
    );

    // Be more lenient with success detection
    const isSuccess =
      textResponse &&
      (textResponse.toLowerCase().includes('success') ||
        textResponse.toLowerCase().includes('ok') ||
        textResponse.toLowerCase().includes('done') ||
        textResponse.toLowerCase().includes('insert') ||
        textResponse.trim() === '' ||
        textResponse === 'empty');

    if (isSuccess) {
      console.log('[PhoneRatings] 🎉 Plain text response indicates success');

      // Resolve all pending votes
      for (const [phoneNumber, pendingVote] of this.pendingVotes.entries()) {
        this.pendingVotes.delete(phoneNumber);
        pendingVote.resolve({
          success: true,
          confirmed: true,
          response: textResponse,
          phoneNumber: pendingVote.phoneNumber,
          isUpvote: pendingVote.isUpvote,
        });
      }
    } else {
      console.log(
        '[PhoneRatings] ❌ Plain text response does not indicate clear success, but resolving anyway',
      );

      // Still resolve as successful but mark as unconfirmed
      for (const [phoneNumber, pendingVote] of this.pendingVotes.entries()) {
        this.pendingVotes.delete(phoneNumber);
        pendingVote.resolve({
          success: true,
          confirmed: false,
          response: textResponse,
          phoneNumber: pendingVote.phoneNumber,
          isUpvote: pendingVote.isUpvote,
          message: `Unclear server response: ${textResponse}`,
        });
      }
    }
  }

  // Initialize connection early (can be called from CompanyDetailsScreen)
  async initializeConnection(retryCount = 0, maxRetries = 3) {
    try {
      console.log(
        `[PhoneRatings] 🔌 Initializing WebSocket connection early... (attempt ${
          retryCount + 1
        }/${maxRetries + 1})`,
      );

      // Check if already connected
      if (this.isConnected()) {
        console.log('[PhoneRatings] ✅ WebSocket already connected');
        return true;
      }

      await this.connect();
      console.log(
        '[PhoneRatings] ✅ WebSocket connection initialized successfully',
      );
      return true;
    } catch (error) {
      console.log(
        `[PhoneRatings] ❌ Failed to initialize WebSocket connection (attempt ${
          retryCount + 1
        }):`,
        error.message,
      );

      // Retry with exponential backoff if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
        console.log(
          `[PhoneRatings] ⏳ Retrying WebSocket connection in ${delay}ms...`,
        );

        return new Promise(resolve => {
          setTimeout(async () => {
            const result = await this.initializeConnection(
              retryCount + 1,
              maxRetries,
            );
            resolve(result);
          }, delay);
        });
      }

      console.log(
        '[PhoneRatings] ❌ Max retry attempts reached, WebSocket initialization failed',
      );
      return false;
    }
  }

  // Check if connection is ready
  isConnected() {
    return this.websocket && this.websocket.readyState === WebSocket.OPEN;
  }

  // Get connection status
  getConnectionStatus() {
    if (!this.websocket) {
      return 'disconnected';
    }

    switch (this.websocket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'closed';
      default:
        return 'unknown';
    }
  }

  // Close WebSocket connection
  disconnect() {
    console.log('[PhoneRatings] 🔌 Disconnecting WebSocket...');
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    // Clear pending votes
    this.pendingVotes.clear();
    this.isConnecting = false;
  }

  // Test connection without sending vote data
  async testConnection() {
    try {
      console.log('[PhoneRatings] 🧪 Testing WebSocket connection...');

      if (this.isConnected()) {
        console.log('[PhoneRatings] ✅ Connection already established');
        return {success: true, message: 'Already connected'};
      }

      const connected = await this.initializeConnection(0, 1); // Single attempt

      if (connected) {
        console.log('[PhoneRatings] ✅ Connection test successful');
        return {success: true, message: 'Connection established'};
      } else {
        console.log('[PhoneRatings] ❌ Connection test failed');
        return {success: false, message: 'Failed to connect'};
      }
    } catch (error) {
      console.log('[PhoneRatings] ❌ Connection test error:', error.message);
      return {success: false, message: error.message};
    }
  }

  // Generate fingerprint for device identification
  getFingerPrint() {
    try {
      // For React Native, we'll use a simplified but more unique fingerprint
      const timestamp = Date.now();
      const random = Math.random();
      const timezone = new Date().getTimezoneOffset();

      const fingerprint = [
        'ReactNative',
        'IndiaCustomerCare',
        'v2.0',
        timezone,
        random,
        timestamp,
      ].join('|');

      return this.generateHashCode(fingerprint);
    } catch (err) {
      console.log('[PhoneRatings] ⚠️ Error generating fingerprint:', err);
      return 'react-native-fallback-' + Date.now();
    }
  }

  // Generate hash code from string
  generateHashCode(str) {
    let hash = 0;
    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
      const chr = str.charCodeAt(i);
      hash = (hash << 5) - hash + chr;
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  }

  // Debug method to get service status
  getDebugInfo() {
    return {
      isConnected: this.isConnected(),
      connectionStatus: this.getConnectionStatus(),
      isConnecting: this.isConnecting,
      pendingVotesCount: this.pendingVotes.size,
      queuedMessagesCount: this.messageQueue.length,
      phoneDataCount: Object.keys(this.phoneData).length,
      websocketUrl: 'wss://phoneratings.tariffplansindia.com:8001',
      license: this.LICENSE,
      fingerprint: this.getFingerPrint(),
    };
  }

  // Send a simple test message to verify server communication
  async sendTestMessage() {
    try {
      console.log('[PhoneRatings] 🧪 Sending test message to server...');

      if (!this.isConnected()) {
        const connected = await this.initializeConnection(0, 2);
        if (!connected) {
          throw new Error('Failed to establish connection for test');
        }
      }

      const testData = {
        l: this.LICENSE,
        op: 'test',
        message: 'ping',
        timestamp: Date.now(),
        finger: this.getFingerPrint(),
      };

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Test message timeout'));
        }, 5000);

        // Store original onmessage handler
        const originalOnMessage = this.websocket.onmessage;

        // Temporary message handler for test
        this.websocket.onmessage = evt => {
          clearTimeout(timeout);
          console.log('[PhoneRatings] 🧪 Test response received:', evt.data);

          // Restore original handler
          this.websocket.onmessage = originalOnMessage;

          resolve({
            success: true,
            response: evt.data,
            message: 'Test message sent and response received',
          });
        };

        // Send test message
        this.websocket.send(JSON.stringify(testData));
        console.log('[PhoneRatings] 🧪 Test message sent:', testData);
      });
    } catch (error) {
      console.log('[PhoneRatings] ❌ Test message failed:', error.message);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Simple test vote function for debugging
  async testVote(phoneNumber = '1234567890') {
    console.log('[PhoneRatings] 🧪 Starting test vote for debugging...');

    try {
      const result = await this.sendVote(phoneNumber, true);
      console.log('[PhoneRatings] 🧪 Test vote result:', result);
      return result;
    } catch (error) {
      console.log('[PhoneRatings] 🧪 Test vote error:', error);
      return {success: false, error: error.message};
    }
  }
}

// PhoneRatings class to handle individual phone number ratings
class PhoneRatings {
  constructor(up, down) {
    if (up === undefined || down === undefined) {
      console.warn('[PhoneRatings] Warning: undefined up or down value');
    }

    this.originalUp = up || 0;
    this.originalDown = down || 0;
    this.remoteUp = up || 0; // value as last updated in remote
    this.remoteDown = down || 0; // value as last updated in remote
    this.remoteDownVal = 0;
    this.remoteUpVal = 0;
    this.changedUp = up || 0;
    this.changedDown = down || 0;
    this.lastModifiedTime = null;
    this.updatedRemotely = false;
  }

  // Check if should update remotely
  shouldUpdateRemotely() {
    if (
      this.remoteDown === this.changedDown &&
      this.remoteUp === this.changedUp
    ) {
      return false; // both same, no change
    }
    if (
      this.lastModifiedTime !== null &&
      new Date() - this.lastModifiedTime > 5000
    ) {
      return true; // no changes but it was already updated and since then unchanged
    }
    return false;
  }

  // Mark as updated remotely
  markUpdatedRemotely() {
    this.updatedRemotely = true;
  }

  // Handle upvote
  doUp() {
    this.lastModifiedTime = null;
    if (this.noChange()) {
      this.changedUp++;
      this.lastModifiedTime = new Date();
    } else if (!this.upSame()) {
      // case 1: changedUp is more
      this.changedUp--; // reset it
      this.lastModifiedTime = new Date();
    } else if (this.upSame() && !this.downSame()) {
      // case 2: changedUp is same but changedDown is more
      this.changedDown--; // reset it
      this.changedUp++;
      this.lastModifiedTime = new Date();
    }
  }

  // Handle downvote
  doDown() {
    this.lastModifiedTime = null;
    console.log('[PhoneRatings] Old rating:', this);
    if (this.noChange()) {
      this.changedDown++;
      this.lastModifiedTime = new Date();
    } else if (!this.downSame()) {
      this.changedDown--; // reset it
      this.lastModifiedTime = new Date();
    } else if (this.downSame() && !this.upSame()) {
      // case 2: changedDown is same but changedUp is more
      this.changedUp--; // reset it
      this.changedDown++;
      this.lastModifiedTime = new Date();
    }
    console.log('[PhoneRatings] Changed rating:', this);
  }

  // Check if no change from original
  noChange() {
    return this.upSame() && this.downSame();
  }

  // Check if up count is same as original
  upSame() {
    return this.originalUp === this.changedUp;
  }

  // Check if down count is same as original
  downSame() {
    return this.originalDown === this.changedDown;
  }
}

// Export singleton instance
export default new PhoneRatingsService();
