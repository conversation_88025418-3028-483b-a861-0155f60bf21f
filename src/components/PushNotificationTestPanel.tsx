import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import {Button} from 'react-native-paper';
import pushNotificationService from '../services/pushNotificationService';
import simpleLocalNotificationService from '../services/simpleLocalNotificationService';
import {COLORS} from '../common';

interface PushNotificationTestPanelProps {
  visible: boolean;
}

const PushNotificationTestPanel: React.FC<PushNotificationTestPanelProps> = ({
  visible,
}) => {
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    setLogs(prev => [...prev, logMessage]);
    console.log(logMessage);
  };

  const clearLogs = () => {
    setLogs([]);
    setTestResult(null);
  };

  const handleAction = async (
    actionName: string,
    action: () => Promise<void> | void,
  ) => {
    setTesting(true);
    addLog(`Starting: ${actionName}`);

    try {
      await action();
      addLog(`✅ ${actionName} completed successfully`);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      addLog(`❌ ${actionName} failed: ${errorMessage}`);
      Alert.alert('Error', `${actionName} failed: ${errorMessage}`);
    } finally {
      setTesting(false);
    }
  };

  const testImmediateNotification = () =>
    handleAction('Test Immediate Notification', () => {
      pushNotificationService.testImmediateNotification();
    });

  const testScheduledNotification = () =>
    handleAction('Test Scheduled Notification (5s)', () => {
      pushNotificationService.testScheduledNotification();
    });

  const testOriginalNotification = () =>
    handleAction('Test Original Notification', () => {
      pushNotificationService.testNotification();
    });

  const testSimpleNotification = () =>
    handleAction('Test Simple Local Notification', () => {
      pushNotificationService.testSimpleLocalNotification();
    });

  const testFirebaseFreeNotification = () =>
    handleAction('Test Firebase-Free Notification', async () => {
      await simpleLocalNotificationService.initialize();
      simpleLocalNotificationService.testSimpleNotification();
    });

  const testFirebaseFreeScheduled = () =>
    handleAction('Test Firebase-Free Scheduled', async () => {
      await simpleLocalNotificationService.initialize();
      simpleLocalNotificationService.testScheduledNotification();
    });

  const openNotificationSettings = () =>
    handleAction('Open Notification Settings', () => {
      simpleLocalNotificationService.openNotificationSettings();
    });

  const checkPermissionsWithGuide = () =>
    handleAction('Check Permissions with Guide', async () => {
      const hasPermissions =
        await simpleLocalNotificationService.checkAndGuidePermissions();
      addLog(`Permissions status: ${hasPermissions ? 'granted' : 'denied'}`);
    });

  const requestPermissions = () =>
    handleAction('Request Permissions', async () => {
      const granted = await pushNotificationService.requestAndroidPermissions();
      addLog(`Permissions granted: ${granted}`);
    });

  const checkPermissions = () =>
    handleAction('Check Permissions', async () => {
      await pushNotificationService.checkNotificationPermissions();
    });

  const reinitializeService = () =>
    handleAction('Reinitialize Service', async () => {
      await pushNotificationService.initialize();
    });

  const testNoteReminder = () =>
    handleAction('Test Note Reminder', () => {
      const reminderDate = new Date(Date.now() + 10000); // 10 seconds from now
      const notificationId = pushNotificationService.scheduleNoteReminder(
        'This is a test note reminder to verify the notification system is working correctly.',
        'Test Company',
        reminderDate,
      );
      addLog(`Scheduled note reminder with ID: ${notificationId}`);
    });

  const testMultipleReminders = () =>
    handleAction('Test Multiple Reminders', () => {
      const baseTime = Date.now() + 15000; // Start 15 seconds from now
      const reminders = [
        {text: 'Test 1', time: baseTime},
        {text: 'Test 2', time: baseTime + 60000}, // 1 minute later
        {text: 'Test 3', time: baseTime + 120000}, // 2 minutes later
      ];

      reminders.forEach((reminder, index) => {
        const notificationId = pushNotificationService.scheduleNoteReminder(
          reminder.text,
          'Test Company',
          new Date(reminder.time),
        );
        addLog(`Scheduled reminder ${index + 1} with ID: ${notificationId}`);
      });
    });

  const showScheduledNotifications = () =>
    handleAction('Show Scheduled Notifications', () => {
      pushNotificationService.getScheduledNotifications();
      addLog('Check console for scheduled notifications list');
    });

  const cancelAllNotifications = () =>
    handleAction('Cancel All Notifications', () => {
      pushNotificationService.cancelAllNotifications();
      addLog('All scheduled notifications cancelled');
    });

  const checkBatteryOptimization = () =>
    handleAction('Check Battery Optimization', async () => {
      await pushNotificationService.checkBatteryOptimization();
      addLog('Check console for battery optimization guidance');
    });

  const testStaggeredReminders = () =>
    handleAction('Test Staggered Reminders', () => {
      const baseTime = Date.now() + 15000; // Start 15 seconds from now
      const reminders = [
        {
          noteText: 'Staggered Test 1',
          companyName: 'Test Company',
          reminderDate: new Date(baseTime),
        },
        {
          noteText: 'Staggered Test 2',
          companyName: 'Test Company',
          reminderDate: new Date(baseTime + 60000),
        },
        {
          noteText: 'Staggered Test 3',
          companyName: 'Test Company',
          reminderDate: new Date(baseTime + 120000),
        },
      ];

      const notificationIds =
        pushNotificationService.scheduleMultipleReminders(reminders);
      addLog(
        `Scheduled ${notificationIds.length} staggered reminders with random offsets`,
      );
    });

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Push Notification Test Panel</Text>
      <Text style={styles.description}>
        Test push notification functionality on Android emulator/device.
        Platform: {Platform.OS} {Platform.Version}
      </Text>

      <ScrollView style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={testFirebaseFreeNotification}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#dc3545">
          🔥 Firebase-Free Test (Try This First!)
        </Button>

        <Button
          mode="contained"
          onPress={testFirebaseFreeScheduled}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#fd7e14">
          ⏰ Firebase-Free Scheduled (5s)
        </Button>

        <Button
          mode="contained"
          onPress={testSimpleNotification}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#28a745">
          🚀 Test Simple Local
        </Button>

        <Button
          mode="contained"
          onPress={testImmediateNotification}
          disabled={testing}
          style={styles.testButton}
          buttonColor={COLORS.primary || '#0a1d50'}>
          Test Immediate Notification
        </Button>

        <Button
          mode="contained"
          onPress={testScheduledNotification}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#28a745">
          Test Scheduled (5 seconds)
        </Button>

        <Button
          mode="contained"
          onPress={testOriginalNotification}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#17a2b8">
          Test Original Method
        </Button>

        <Button
          mode="contained"
          onPress={testNoteReminder}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#fd7e14">
          Test Note Reminder (10s)
        </Button>

        <Button
          mode="contained"
          onPress={testMultipleReminders}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#e83e8c">
          Test Multiple Reminders
        </Button>

        <Button
          mode="contained"
          onPress={showScheduledNotifications}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#17a2b8">
          Show Scheduled
        </Button>

        <Button
          mode="contained"
          onPress={cancelAllNotifications}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#dc3545">
          Cancel All
        </Button>

        <Button
          mode="contained"
          onPress={checkBatteryOptimization}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#fd7e14">
          Check Battery Optimization
        </Button>

        <Button
          mode="contained"
          onPress={testStaggeredReminders}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#20c997">
          Test Staggered Reminders
        </Button>

        <Button
          mode="contained"
          onPress={checkPermissionsWithGuide}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#e83e8c">
          🔧 Check Permissions + Guide
        </Button>

        <Button
          mode="contained"
          onPress={openNotificationSettings}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#17a2b8">
          ⚙️ Open Settings
        </Button>

        <Button
          mode="contained"
          onPress={requestPermissions}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#e83e8c">
          Request Permissions
        </Button>

        <Button
          mode="contained"
          onPress={checkPermissions}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#6f42c1">
          Check Permissions
        </Button>

        <Button
          mode="contained"
          onPress={reinitializeService}
          disabled={testing}
          style={styles.testButton}
          buttonColor="#dc3545">
          Reinitialize Service
        </Button>

        <Button
          mode="outlined"
          onPress={clearLogs}
          disabled={testing}
          style={styles.clearButton}>
          Clear Logs
        </Button>
      </ScrollView>

      {logs.length > 0 && (
        <View style={styles.logContainer}>
          <Text style={styles.logTitle}>Test Logs:</Text>
          <ScrollView style={styles.logScroll} nestedScrollEnabled>
            {logs.map((log, index) => (
              <Text key={index} style={styles.logText}>
                {log}
              </Text>
            ))}
          </ScrollView>
        </View>
      )}

      {testResult && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultText}>{testResult}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
    maxHeight: '80%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
  buttonContainer: {
    maxHeight: 300,
  },
  testButton: {
    marginVertical: 4,
  },
  clearButton: {
    marginTop: 8,
    marginVertical: 4,
  },
  logContainer: {
    marginTop: 16,
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#dee2e6',
    maxHeight: 200,
  },
  logTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    padding: 8,
    backgroundColor: '#e9ecef',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  logScroll: {
    maxHeight: 160,
  },
  logText: {
    fontSize: 12,
    color: '#495057',
    paddingHorizontal: 8,
    paddingVertical: 2,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  resultContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#d4edda',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#c3e6cb',
  },
  resultText: {
    fontSize: 14,
    color: '#155724',
    textAlign: 'center',
  },
});

export default PushNotificationTestPanel;
