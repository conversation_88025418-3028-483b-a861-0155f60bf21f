import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import PhoneRatingsService from '../socket/client.js';

const WebSocketDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const updateDebugInfo = () => {
    const info = PhoneRatingsService.getDebugInfo();
    setDebugInfo(info);
    console.log('[WebSocketDebug] Current debug info:', info);
  };

  const testConnection = async () => {
    setIsLoading(true);
    try {
      console.log('[WebSocketDebug] Testing connection...');
      const result = await PhoneRatingsService.testConnection();
      console.log('[WebSocketDebug] Connection test result:', result);
      
      setTestResults(prev => [
        ...prev,
        {
          type: 'Connection Test',
          timestamp: new Date().toLocaleTimeString(),
          result,
        },
      ]);
      
      Alert.alert(
        'Connection Test',
        result.success ? 'Connection successful!' : `Failed: ${result.message}`,
      );
    } catch (error) {
      console.error('[WebSocketDebug] Connection test error:', error);
      Alert.alert('Error', `Connection test failed: ${error}`);
    } finally {
      setIsLoading(false);
      updateDebugInfo();
    }
  };

  const testVote = async () => {
    setIsLoading(true);
    try {
      console.log('[WebSocketDebug] Testing vote...');
      const result = await PhoneRatingsService.testVote('9999999999');
      console.log('[WebSocketDebug] Vote test result:', result);
      
      setTestResults(prev => [
        ...prev,
        {
          type: 'Vote Test',
          timestamp: new Date().toLocaleTimeString(),
          result,
        },
      ]);
      
      Alert.alert(
        'Vote Test',
        result.success 
          ? `Vote successful! ${result.timeout ? '(with timeout)' : ''}` 
          : `Failed: ${result.error || 'Unknown error'}`,
      );
    } catch (error) {
      console.error('[WebSocketDebug] Vote test error:', error);
      Alert.alert('Error', `Vote test failed: ${error}`);
    } finally {
      setIsLoading(false);
      updateDebugInfo();
    }
  };

  const testMessage = async () => {
    setIsLoading(true);
    try {
      console.log('[WebSocketDebug] Testing message...');
      const result = await PhoneRatingsService.sendTestMessage();
      console.log('[WebSocketDebug] Message test result:', result);
      
      setTestResults(prev => [
        ...prev,
        {
          type: 'Message Test',
          timestamp: new Date().toLocaleTimeString(),
          result,
        },
      ]);
      
      Alert.alert(
        'Message Test',
        result.success ? 'Message sent and received!' : `Failed: ${result.error}`,
      );
    } catch (error) {
      console.error('[WebSocketDebug] Message test error:', error);
      Alert.alert('Error', `Message test failed: ${error}`);
    } finally {
      setIsLoading(false);
      updateDebugInfo();
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setDebugInfo(null);
  };

  React.useEffect(() => {
    updateDebugInfo();
  }, []);

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>WebSocket Debug Panel</Text>
      
      {/* Debug Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Status</Text>
        <TouchableOpacity style={styles.button} onPress={updateDebugInfo}>
          <Text style={styles.buttonText}>Refresh Status</Text>
        </TouchableOpacity>
        
        {debugInfo && (
          <View style={styles.infoContainer}>
            <Text style={styles.infoText}>Connected: {debugInfo.isConnected ? '✅' : '❌'}</Text>
            <Text style={styles.infoText}>Status: {debugInfo.connectionStatus}</Text>
            <Text style={styles.infoText}>Connecting: {debugInfo.isConnecting ? 'Yes' : 'No'}</Text>
            <Text style={styles.infoText}>Pending Votes: {debugInfo.pendingVotesCount}</Text>
            <Text style={styles.infoText}>Queued Messages: {debugInfo.queuedMessagesCount}</Text>
            <Text style={styles.infoText}>Phone Data Count: {debugInfo.phoneDataCount}</Text>
            <Text style={styles.infoText}>License: {debugInfo.license}</Text>
            <Text style={styles.infoText}>Fingerprint: {debugInfo.fingerprint}</Text>
          </View>
        )}
      </View>

      {/* Test Buttons */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Tests</Text>
        
        <TouchableOpacity 
          style={[styles.button, styles.testButton]} 
          onPress={testConnection}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Connection</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.testButton]} 
          onPress={testMessage}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Message</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.testButton]} 
          onPress={testVote}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Vote</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      {/* Test Results */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Results</Text>
        {testResults.map((test, index) => (
          <View key={index} style={styles.resultContainer}>
            <Text style={styles.resultTitle}>
              {test.type} - {test.timestamp}
            </Text>
            <Text style={styles.resultText}>
              {JSON.stringify(test.result, null, 2)}
            </Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    marginBottom: 20,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  testButton: {
    backgroundColor: '#34C759',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  infoContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: 6,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#333',
  },
  resultContainer: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: 6,
  },
  resultTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  resultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#666',
  },
});

export default WebSocketDebugPanel;
