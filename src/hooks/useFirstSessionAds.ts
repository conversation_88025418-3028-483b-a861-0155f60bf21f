import {useState, useEffect} from 'react';
import appStateService from '../services/appStateService';

interface FirstSessionAdsState {
  shouldShowAds: boolean | null; // null means loading
  isLoading: boolean;
  error: string | null;
}

export const useFirstSessionAds = (): FirstSessionAdsState => {
  const [shouldShowAds, setShouldShowAds] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check first session ads status on mount
  useEffect(() => {
    checkFirstSessionAdsStatus();
  }, []);

  const checkFirstSessionAdsStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('[useFirstSessionAds] Checking first session ads status...');

      // Check if ads should be shown in this session
      const shouldShow = await appStateService.shouldShowAdsInFirstSession();

      setShouldShowAds(shouldShow);

      console.log(`[useFirstSessionAds] shouldShowAds set to: ${shouldShow}`);

      if (shouldShow) {
        console.log('[useFirstSessionAds] ✅ Ads will be shown');
      } else {
        console.log(
          '[useFirstSessionAds] ❌ Ads will be hidden (first session)',
        );
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error(
        '[useFirstSessionAds] Error checking first session ads:',
        err,
      );

      // Default to not showing ads in case of error
      setShouldShowAds(false);
      console.log(
        '[useFirstSessionAds] ❌ Error occurred - ads will be hidden',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return {
    shouldShowAds,
    isLoading,
    error,
  };
};
